"""
Tests for Device Management Service

This module contains comprehensive tests for the DeviceManagementService
to ensure all device-related operations work correctly.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from django.test import TestCase, RequestFactory
from user.services.device_management_service import DeviceManagementService
from user.models import User


class TestDeviceManagementService(TestCase):
    """Test cases for DeviceManagementService"""

    def setUp(self):
        """Set up test fixtures"""
        self.factory = RequestFactory()
        self.service = DeviceManagementService()
        self.unique_id = "test-unique-id"
        self.user = User.objects.create_user(
            email="<EMAIL>",
            name="Test User",
            password="testpass123"
        )

    @patch('user.services.device_management_service.secrets')
    @patch('user.services.device_management_service.timezone')
    def test_generate_device_id(self, mock_timezone, mock_secrets):
        """Test device ID generation"""
        # Arrange
        mock_timezone.now.return_value.strftime.return_value = "20240101120000"
        mock_secrets.token_urlsafe.return_value = "test-token-32-chars-long-enough"

        # Act
        device_id = self.service.generate_device_id()

        # Assert
        expected = "20240101120000_test-token-32-chars-long-enough"
        self.assertEqual(device_id, expected)
        mock_timezone.now.assert_called_once()
        mock_secrets.token_urlsafe.assert_called_once_with(32)

    @patch('user.services.device_management_service.get_dynamic_device_info')
    @patch('user.services.device_management_service.get_client_ip')
    @patch('user.services.device_management_service.generate_device_fingerprint')
    def test_extract_device_info(self, mock_fingerprint, mock_client_ip, mock_device_info):
        """Test device information extraction"""
        # Arrange
        mock_device_info.return_value = {
            "device_name": "Test Device",
            "device_type": "web"
        }
        mock_client_ip.return_value = "***********"
        mock_fingerprint.return_value = "test-fingerprint"
        
        request = self.factory.post('/register/')
        request.META['HTTP_USER_AGENT'] = "Test User Agent"

        # Act
        result = self.service.extract_device_info(request, "registration")

        # Assert
        self.assertEqual(result['device_name'], "Test Device")
        self.assertEqual(result['device_type'], "web")
        self.assertEqual(result['client_ip'], "***********")
        self.assertEqual(result['fingerprint'], "test-fingerprint")
        self.assertEqual(result['user_agent'], "Test User Agent")
        self.assertEqual(result['full_device_info']['device_name'], "Test Device")

    @patch('user.services.device_management_service.DeviceAuthenticationService')
    @patch('user.services.device_management_service.log_operation_info')
    def test_register_device_success(self, mock_log, mock_device_auth):
        """Test successful device registration"""
        # Arrange
        mock_device_auth.register_device.return_value = None  # Success
        request = Mock()

        # Act
        success, error = self.service.register_device(
            self.user, "test-device-id", "Test Device", "web", request, self.unique_id
        )

        # Assert
        self.assertTrue(success)
        self.assertIsNone(error)
        mock_device_auth.register_device.assert_called_once_with(
            self.user, "test-device-id", "Test Device", "web", request
        )
        mock_log.assert_called_once()

    @patch('user.services.device_management_service.DeviceAuthenticationService')
    @patch('user.services.device_management_service.log_operation_info')
    def test_register_device_failure(self, mock_log, mock_device_auth):
        """Test device registration failure"""
        # Arrange
        mock_device_auth.register_device.side_effect = Exception("Registration failed")
        request = Mock()

        # Act
        success, error = self.service.register_device(
            self.user, "test-device-id", "Test Device", "web", request, self.unique_id
        )

        # Assert
        self.assertFalse(success)
        self.assertEqual(error, "Registration failed")
        mock_log.assert_called_with(
            self.unique_id,
            "DEVICE_REGISTRATION",
            "Failed to register device during registration",
            metadata={
                "user_email": self.user.email,
                "device_id": "test-device-id",
                "device_name": "Test Device",
                "device_type": "web",
                "error": "Registration failed",
            },
            level="WARNING",
        )

    @patch('user.services.device_management_service.device_validation_service')
    @patch('user.services.device_management_service.log_operation_info')
    def test_track_registration_device_success(self, mock_log, mock_device_validation):
        """Test successful device tracking"""
        # Arrange
        mock_device_validation.track_registration_device.return_value = {"tracking": "success"}
        request = Mock()

        # Act
        result = self.service.track_registration_device(
            self.user, "test-device-id", request, self.unique_id
        )

        # Assert
        self.assertEqual(result, {"tracking": "success"})
        mock_device_validation.track_registration_device.assert_called_once_with(
            user=self.user,
            device_id="test-device-id",
            request=request,
            unique_id=self.unique_id
        )

    @patch('user.services.device_management_service.device_validation_service')
    @patch('user.services.device_management_service.log_operation_info')
    def test_track_registration_device_failure(self, mock_log, mock_device_validation):
        """Test device tracking failure"""
        # Arrange
        mock_device_validation.track_registration_device.side_effect = Exception("Tracking failed")
        request = Mock()

        # Act
        result = self.service.track_registration_device(
            self.user, "test-device-id", request, self.unique_id
        )

        # Assert
        self.assertEqual(result, {})
        mock_log.assert_called_with(
            self.unique_id,
            "DEVICE_TRACKING",
            f"Failed to track device for user {self.user.email}",
            metadata={
                "user_email": self.user.email,
                "device_id": "test-device-id",
                "error": "Tracking failed",
                "tracking_successful": False,
            },
            level="WARNING",
        )

    @patch.object(DeviceManagementService, 'register_device')
    @patch.object(DeviceManagementService, 'track_registration_device')
    def test_handle_device_registration_flow(self, mock_track, mock_register):
        """Test complete device registration flow"""
        # Arrange
        mock_register.return_value = (True, None)
        mock_track.return_value = {"tracking": "success"}
        
        device_data = {
            "device_id": "test-device-id",
            "device_name": "Test Device",
            "device_type": "web"
        }
        request = Mock()

        # Act
        result = self.service.handle_device_registration_flow(
            self.user, device_data, request, self.unique_id
        )

        # Assert
        self.assertTrue(result["device_registered"])
        self.assertIsNone(result["registration_error"])
        self.assertEqual(result["tracking_info"], {"tracking": "success"})
        self.assertEqual(result["device_id"], "test-device-id")
        self.assertEqual(result["device_name"], "Test Device")
        self.assertEqual(result["device_type"], "web")
        
        mock_register.assert_called_once_with(
            self.user, "test-device-id", "Test Device", "web", request, self.unique_id
        )
        mock_track.assert_called_once_with(
            self.user, "test-device-id", request, self.unique_id
        )


if __name__ == "__main__":
    pytest.main([__file__])
