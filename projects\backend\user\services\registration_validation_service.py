"""
Registration Validation Service

This service handles all validation logic for user registration including:
- Rate limiting validation
- Device validation
- Duplicate user checks
- Input data validation
"""

from typing import Dict, Any, Tu<PERSON>, Optional
from django.utils import timezone
from oauth2_auth.utils import get_client_ip, generate_device_fingerprint, get_dynamic_device_info
from oauth2_auth.device_validation_service import device_validation_service
from oauth2_auth.rate_limiting_service import rate_limiting_service
from oauth2_auth.authentication import DeviceAuthenticationService
from agritram.exceptions import (
    ValidationException,
    DuplicateResourceException,
    raise_validation_error,
)
from agritram.logger_utils import (
    log_operation_info,
    log_security_event_standardized,
    generate_unique_request_id,
)
from user.models import User


class RegistrationValidationService:
    """Service for handling all registration validation logic"""

    @staticmethod
    def validate_rate_limiting(request, unique_id: str) -> None:
        """
        Validate rate limiting for registration attempts
        
        Args:
            request: HTTP request object
            unique_id: Unique request ID for correlation
            
        Raises:
            ValidationException: If rate limit is exceeded
        """
        client_ip = get_client_ip(request)
        rate_check_id = f"{client_ip}_registration"
        
        is_allowed, rate_info = rate_limiting_service.check_rate_limit(
            rate_check_id, "registration", request
        )
        
        if not is_allowed:
            raise_validation_error(
                message="Too many registration attempts",
                details=rate_info.get("message", "Please try again later"),
                error_code="RATE_LIMITED",
            )

    @staticmethod
    def validate_device_data(request, unique_id: str) -> Dict[str, Any]:
        """
        Validate and process device-related data
        
        Args:
            request: HTTP request object
            unique_id: Unique request ID for correlation
            
        Returns:
            Dict containing validated device information
            
        Raises:
            ValidationException: If device validation fails
        """
        import secrets
        
        # Extract registration data
        email = request.data.get("email", "").lower().strip()
        device_id = request.data.get("device_id")
        provided_device_type = request.data.get("device_type")

        # Generate device_id if not provided (for device tracking)
        if not device_id:
            # Use cryptographically secure random generation
            device_id = (
                f"{timezone.now().strftime('%Y%m%d%H%M%S')}_{secrets.token_urlsafe(32)}"
            )

        # Validate device_id format and length for security
        if not device_id or len(device_id) < 58:
            raise_validation_error(
                message="Invalid device identifier",
                details="Device ID must be at least 58 characters long",
            )

        # Get dynamic device information
        device_info = get_dynamic_device_info(request, "registration")
        device_name = device_info["device_name"]
        detected_device_type = device_info["device_type"]

        # Validate device_type consistency if provided
        if provided_device_type:
            if provided_device_type not in ["web", "mobile", "desktop", "api"]:
                raise_validation_error(
                    message="Invalid device type",
                    details="Device type must be one of: web, mobile, desktop, api",
                )

            # Check if provided device_type matches detected device_type
            if provided_device_type != detected_device_type:
                log_operation_info(
                    unique_id,
                    "DEVICE_TYPE_MISMATCH",
                    f"Device type mismatch during registration: provided={provided_device_type}, detected={detected_device_type}",
                    metadata={
                        "email": email,
                        "device_id": device_id,
                        "provided_device_type": provided_device_type,
                        "detected_device_type": detected_device_type,
                        "user_agent": request.META.get("HTTP_USER_AGENT", ""),
                    },
                    level="WARNING",
                )
                # Use provided device_type but log the discrepancy
                device_type = provided_device_type
            else:
                device_type = provided_device_type
        else:
            device_type = detected_device_type

        return {
            "device_id": device_id,
            "device_name": device_name,
            "device_type": device_type,
            "detected_device_type": detected_device_type,
            "provided_device_type": provided_device_type,
            "email": email,
        }

    @staticmethod
    def validate_device_registration(device_data: Dict[str, Any], request, unique_id: str) -> None:
        """
        Validate device against existing devices in database if provided by user
        
        Args:
            device_data: Device information dictionary
            request: HTTP request object
            unique_id: Unique request ID for correlation
            
        Raises:
            ValidationException: If device validation fails
        """
        # Only validate if explicitly provided by user
        if request.data.get("device_id"):
            validation_result = (
                DeviceAuthenticationService.validate_device_for_registration(
                    device_id=device_data["device_id"],
                    device_type=device_data["device_type"],
                    request=request,
                    unique_id=unique_id,
                )
            )

            if not validation_result["is_valid"]:
                # Device validation failed - raise error
                raise_validation_error(
                    message="Device validation failed",
                    details=validation_result["message"],
                )

            # Log any warnings from validation
            if validation_result["warnings"]:
                log_operation_info(
                    unique_id,
                    "DEVICE_VALIDATION_WARNINGS",
                    f"Device validation warnings for registration: {', '.join(validation_result['warnings'])}",
                    metadata={
                        "email": device_data["email"],
                        "device_id": device_data["device_id"],
                        "warnings": validation_result["warnings"],
                        "validation_details": validation_result["details"],
                    },
                    level="WARNING",
                )

        # Log comprehensive device validation for tracking purposes
        log_operation_info(
            unique_id,
            "DEVICE_VALIDATION_COMPLETE",
            f"Device validation completed for registration: {device_data['device_id']}",
            metadata={
                "device_id": device_data["device_id"],
                "email": device_data["email"],
                "device_name": device_data["device_name"],
                "device_type": device_data["device_type"],
                "detected_device_type": device_data["detected_device_type"],
                "provided_device_type": device_data["provided_device_type"],
                "device_id_provided_by_user": bool(request.data.get("device_id")),
                "validation_passed": True,
            },
        )

    @staticmethod
    def validate_duplicate_user(email: str, unique_id: str, request) -> None:
        """
        Check for duplicate registration attempts
        
        Args:
            email: User email to check
            unique_id: Unique request ID for correlation
            request: HTTP request object
            
        Raises:
            DuplicateResourceException: If user already exists
        """
        if email:
            try:
                existing_user = User.objects.get(email=email)
                if existing_user:
                    # Log security event with standardized logging
                    log_security_event_standardized(
                        unique_id,
                        "DUPLICATE_REGISTRATION_ATTEMPT",
                        "Registration attempt with existing active email",
                        user=existing_user,
                        request=request,
                        metadata={"email": email},
                        level="WARNING",
                    )
                    raise DuplicateResourceException(
                        message="User with this email already exists",
                        details="An active account with this email address is already registered",
                    )
            except User.DoesNotExist:
                pass

    @classmethod
    def validate_registration_request(cls, request, unique_id: str) -> Dict[str, Any]:
        """
        Perform complete validation of registration request
        
        Args:
            request: HTTP request object
            unique_id: Unique request ID for correlation
            
        Returns:
            Dict containing validated data
            
        Raises:
            ValidationException: If any validation fails
            DuplicateResourceException: If user already exists
        """
        # Validate rate limiting
        cls.validate_rate_limiting(request, unique_id)
        
        # Validate and process device data
        device_data = cls.validate_device_data(request, unique_id)
        
        # Validate device registration
        cls.validate_device_registration(device_data, request, unique_id)
        
        # Check for duplicate users
        cls.validate_duplicate_user(device_data["email"], unique_id, request)
        
        return device_data
