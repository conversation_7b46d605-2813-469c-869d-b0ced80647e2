"""
Tests for Registration Validation Service

This module contains comprehensive tests for the RegistrationValidationService
to ensure all validation logic works correctly.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from django.test import TestCase, RequestFactory
from django.utils import timezone
from agritram.exceptions import ValidationException, DuplicateResourceException
from user.services.registration_validation_service import RegistrationValidationService
from user.models import User


class TestRegistrationValidationService(TestCase):
    """Test cases for RegistrationValidationService"""

    def setUp(self):
        """Set up test fixtures"""
        self.factory = RequestFactory()
        self.service = RegistrationValidationService()
        self.unique_id = "test-unique-id"

    @patch("user.services.registration_validation_service.rate_limiting_service")
    @patch("user.services.registration_validation_service.get_client_ip")
    def test_validate_rate_limiting_success(
        self, mock_get_client_ip, mock_rate_service
    ):
        """Test successful rate limiting validation"""
        # Arrange
        mock_get_client_ip.return_value = "***********"
        mock_rate_service.check_rate_limit.return_value = (True, {})
        request = self.factory.post("/register/")

        # Act & Assert - Should not raise exception
        self.service.validate_rate_limiting(request, self.unique_id)

        # Verify rate limiting was checked
        mock_rate_service.check_rate_limit.assert_called_once_with(
            "***********_registration", "registration", request
        )

    @patch("user.services.registration_validation_service.rate_limiting_service")
    @patch("user.services.registration_validation_service.get_client_ip")
    @patch("user.services.registration_validation_service.raise_validation_error")
    def test_validate_rate_limiting_failure(
        self, mock_raise_error, mock_get_client_ip, mock_rate_service
    ):
        """Test rate limiting validation failure"""
        # Arrange
        mock_get_client_ip.return_value = "***********"
        mock_rate_service.check_rate_limit.return_value = (
            False,
            {"message": "Rate limited"},
        )
        request = self.factory.post("/register/")

        # Act
        self.service.validate_rate_limiting(request, self.unique_id)

        # Assert
        mock_raise_error.assert_called_once_with(
            message="Too many registration attempts",
            details="Rate limited",
            error_code="RATE_LIMITED",
        )

    @patch("user.services.registration_validation_service.get_dynamic_device_info")
    @patch("user.services.registration_validation_service.secrets")
    @patch("user.services.registration_validation_service.timezone")
    def test_validate_device_data_no_device_id(
        self, mock_timezone, mock_secrets, mock_device_info
    ):
        """Test device data validation when no device_id is provided"""
        # Arrange
        mock_timezone.now.return_value.strftime.return_value = "20240101120000"
        mock_secrets.token_urlsafe.return_value = "test-token-32-chars-long-enough"
        mock_device_info.return_value = {
            "device_name": "Test Device",
            "device_type": "web",
        }

        request = self.factory.post(
            "/register/",
            {
                "email": "<EMAIL>",
                "name": "Test User",
                "password": "testpass123",
            },
        )

        # Act
        result = self.service.validate_device_data(request, self.unique_id)

        # Assert
        self.assertEqual(result["email"], "<EMAIL>")
        self.assertEqual(result["device_name"], "Test Device")
        self.assertEqual(result["device_type"], "web")
        self.assertEqual(result["detected_device_type"], "web")
        self.assertIsNone(result["provided_device_type"])
        self.assertTrue(len(result["device_id"]) >= 58)

    @patch("user.services.registration_validation_service.get_dynamic_device_info")
    def test_validate_device_data_with_device_id(self, mock_device_info):
        """Test device data validation when device_id is provided"""
        # Arrange
        mock_device_info.return_value = {
            "device_name": "Test Device",
            "device_type": "mobile",
        }

        device_id = "20240101120000_" + "a" * 32  # 58+ characters
        request = self.factory.post(
            "/register/",
            {
                "email": "<EMAIL>",
                "device_id": device_id,
                "device_type": "mobile",
            },
        )

        # Act
        result = self.service.validate_device_data(request, self.unique_id)

        # Assert
        self.assertEqual(result["device_id"], device_id)
        self.assertEqual(result["device_type"], "mobile")
        self.assertEqual(result["provided_device_type"], "mobile")

    @patch("user.services.registration_validation_service.raise_validation_error")
    def test_validate_device_data_invalid_device_id(self, mock_raise_error):
        """Test device data validation with invalid device_id"""
        # Arrange
        request = self.factory.post(
            "/register/",
            {"email": "<EMAIL>", "device_id": "short"},  # Too short
        )

        # Act
        self.service.validate_device_data(request, self.unique_id)

        # Assert
        mock_raise_error.assert_called_once_with(
            message="Invalid device identifier",
            details="Device ID must be at least 58 characters long",
        )

    @patch("user.services.registration_validation_service.raise_validation_error")
    @patch("user.services.registration_validation_service.get_dynamic_device_info")
    def test_validate_device_data_invalid_device_type(
        self, mock_device_info, mock_raise_error
    ):
        """Test device data validation with invalid device_type"""
        # Arrange
        mock_device_info.return_value = {
            "device_name": "Test Device",
            "device_type": "web",
        }

        device_id = "20240101120000_" + "a" * 32
        request = self.factory.post(
            "/register/",
            {
                "email": "<EMAIL>",
                "device_id": device_id,
                "device_type": "invalid_type",
            },
        )

        # Act
        self.service.validate_device_data(request, self.unique_id)

        # Assert
        mock_raise_error.assert_called_once_with(
            message="Invalid device type",
            details="Device type must be one of: web, mobile, desktop, api",
        )

    @patch("user.services.registration_validation_service.DeviceAuthenticationService")
    @patch("user.services.registration_validation_service.raise_validation_error")
    def test_validate_device_registration_failure(
        self, mock_raise_error, mock_device_auth
    ):
        """Test device registration validation failure"""
        # Arrange
        mock_device_auth.validate_device_for_registration.return_value = {
            "is_valid": False,
            "message": "Device validation failed",
        }

        device_data = {
            "device_id": "test-device-id",
            "device_type": "web",
            "email": "<EMAIL>",
        }

        request = self.factory.post("/register/", {"device_id": "test-device-id"})

        # Act
        self.service.validate_device_registration(device_data, request, self.unique_id)

        # Assert
        mock_raise_error.assert_called_once_with(
            message="Device validation failed",
            details="Device validation failed",
        )

    def test_validate_duplicate_user_no_existing_user(self):
        """Test duplicate user validation when no existing user"""
        # Act & Assert - Should not raise exception
        self.service.validate_duplicate_user("<EMAIL>", self.unique_id, Mock())

    def test_validate_duplicate_user_existing_user(self):
        """Test duplicate user validation when user exists"""
        # Arrange
        User.objects.create_user(
            email="<EMAIL>", name="Existing User", password="testpass123"
        )

        # Act & Assert
        with self.assertRaises(DuplicateResourceException):
            self.service.validate_duplicate_user(
                "<EMAIL>", self.unique_id, Mock()
            )

    @patch.object(RegistrationValidationService, "validate_rate_limiting")
    @patch.object(RegistrationValidationService, "validate_device_data")
    @patch.object(RegistrationValidationService, "validate_device_registration")
    @patch.object(RegistrationValidationService, "validate_duplicate_user")
    def test_validate_registration_request_success(
        self, mock_duplicate, mock_device_reg, mock_device_data, mock_rate_limit
    ):
        """Test complete registration request validation success"""
        # Arrange
        mock_device_data.return_value = {
            "email": "<EMAIL>",
            "device_id": "test-id",
        }
        request = Mock()

        # Act
        result = self.service.validate_registration_request(request, self.unique_id)

        # Assert
        self.assertEqual(result["email"], "<EMAIL>")
        mock_rate_limit.assert_called_once_with(request, self.unique_id)
        mock_device_data.assert_called_once_with(request, self.unique_id)
        mock_device_reg.assert_called_once()
        mock_duplicate.assert_called_once_with(
            "<EMAIL>", self.unique_id, request
        )


if __name__ == "__main__":
    pytest.main([__file__])
